import { Injectable } from '@nestjs/common';
import { ActionEnum, ClientEnum, LocaleEnum, NotificationGroupEnum } from '@prisma/client';

import { ActionDict } from '../../../dictionaries/actions.dict';
import { LabelsDict } from '../../../dictionaries/group-labels.dict';
import { PlatformDict } from '../../../dictionaries/platforms.dict';
import { SettingsDict } from '../../../dictionaries/settings.dict';
import { UserRoleEnum } from '../../../shared/enums/user.enum';
import { GroupSettingsParams } from '../../domain/models/notification-settings.model';
import {
    ActionSchemaType,
    GroupSchemaType,
    Localization,
    MetaSchemaConstructor,
    NOTIFICATION_GROUPS,
} from '../../notifications.schema';

type LocalizationDictionary<T extends ActionEnum | ClientEnum | NotificationGroupEnum> = Record<T, Localization>;

@Injectable()
export class NotificationSchemaService {
    private readonly NOTIFICATION_GROUPS = NOTIFICATION_GROUPS;
    private STUDENT_GROUP_CODES: NotificationGroupEnum[];
    private TEACHER_GROUP_CODES: NotificationGroupEnum[];
    private CUSTOM_GROUP_CODES: NotificationGroupEnum[];
    private SYSTEM_GROUP_CODES: NotificationGroupEnum[];
    private ACTIONS_MAP = new Map<ActionEnum, ActionSchemaType>();

    private ACTION_TO_GROUP_MAP: Record<ActionEnum, NotificationGroupEnum> = {} as Record<
        ActionEnum,
        NotificationGroupEnum
    >;

    constructor() {
        this.STUDENT_GROUP_CODES = this.getUserModifiedGroupsByRole(UserRoleEnum.student).map((group) => group.code);
        this.TEACHER_GROUP_CODES = this.getUserModifiedGroupsByRole(UserRoleEnum.teacher).map((group) => group.code);
        this.SYSTEM_GROUP_CODES = this.getUserModifiedGroupsByRole(UserRoleEnum.system).map((group) => group.code);
        this.ACTION_TO_GROUP_MAP = this.getActionToGroupMap();
        this.fillActionsMap();
        this.fillCustomGroups();
    }

    public getDefaultGroupSettings(): GroupSettingsParams {
        const groups = {};
        Object.values(NotificationGroupEnum).forEach((group) => {
            groups[group] = [ClientEnum.PLATFORM];
        });
        return groups;
    }

    public getMaps(locale: LocaleEnum = LocaleEnum.ru): {
        actions: Record<ActionEnum, string>;
        clients: Record<ClientEnum, string>;
        settings: Record<NotificationGroupEnum, string>;
        labels: Record<NotificationGroupEnum, string>;
    } {
        return {
            actions: this.getLocalizedDictionary(ActionDict, locale),
            clients: this.getLocalizedDictionary(PlatformDict, locale),
            settings: this.getLocalizedDictionary(SettingsDict, locale),
            labels: this.getLocalizedDictionary(LabelsDict, locale),
        };
    }

    private getLocalizedDictionary<T extends ActionEnum | ClientEnum | NotificationGroupEnum>(
        dictionary: LocalizationDictionary<T>,
        locale: LocaleEnum,
    ): Record<T, string> {
        const localizedMessages: Partial<Record<T, string>> = {};

        for (const key in dictionary) {
            // eslint-disable-next-line no-prototype-builtins
            if (dictionary.hasOwnProperty(key)) {
                const sentence = dictionary[key];
                const typedKey = key as unknown as T;
                localizedMessages[typedKey] = sentence[locale];
            }
        }

        return localizedMessages as Record<T, string>;
    }

    /** @deprecated Используется в репозитории для конвертирования старых настроек в версию 2 */
    public mapActionsToGroups(actions: ActionEnum[]): NotificationGroupEnum[] {
        const groups: NotificationGroupEnum[] = [];

        actions.forEach((action) => {
            const group = this.getGroupByAction(action);
            if (group) {
                groups.push(group);
            }
        });

        return [...new Set(groups)];
    }

    public getGroupByAction(action: ActionEnum): NotificationGroupEnum {
        return this.ACTION_TO_GROUP_MAP[action];
    }

    public isSystemAction(action: ActionEnum): boolean {
        return this.SYSTEM_GROUP_CODES.includes(this.getGroupByAction(action));
    }

    /** Withdraw, Tariffs, Subscriptions  (нет школы) */
    public isSystemGroup(group: NotificationGroupEnum): boolean {
        // см. notifications.schema UserRoleEnum.system
        return this.SYSTEM_GROUP_CODES.includes(group);
    }

    public getMetaSchema(action: ActionEnum): MetaSchemaConstructor {
        return this.ACTIONS_MAP.get(action)?.schema;
    }

    public getCustomGroupCodes(): NotificationGroupEnum[] {
        return this.CUSTOM_GROUP_CODES;
    }

    public getTeacherGroupCodes(): NotificationGroupEnum[] {
        return this.TEACHER_GROUP_CODES;
    }

    public getStudentGroupCodes(): NotificationGroupEnum[] {
        return this.STUDENT_GROUP_CODES;
    }

    public getSystemGroupCodes(): NotificationGroupEnum[] {
        return this.SYSTEM_GROUP_CODES;
    }

    private fillActionsMap(): void {
        this.NOTIFICATION_GROUPS.forEach((group) => {
            group.actions.forEach((action) => {
                this.ACTIONS_MAP.set(action.code, action);
            });
        });
    }

    private getUserModifiedGroupsByRole(role: UserRoleEnum): GroupSchemaType[] {
        return this.NOTIFICATION_GROUPS.filter((group) => group.users.includes(role));
    }

    private fillCustomGroups(): void {
        this.CUSTOM_GROUP_CODES = this.NOTIFICATION_GROUPS.filter(
            (group) => group.users.includes(UserRoleEnum.student) || group.users.includes(UserRoleEnum.teacher),
        ).map((group) => group.code);
    }

    private getActionToGroupMap(): Record<ActionEnum, NotificationGroupEnum> {
        const dictionary: Record<ActionEnum, NotificationGroupEnum> = {} as Record<ActionEnum, NotificationGroupEnum>;

        this.NOTIFICATION_GROUPS.forEach((setting) => {
            setting.actions.forEach((action) => {
                dictionary[action.code] = setting.code;
            });
        });

        return dictionary;
    }
}
