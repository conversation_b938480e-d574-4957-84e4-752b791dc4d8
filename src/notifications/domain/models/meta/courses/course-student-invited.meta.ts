import { LocaleEnum } from '@prisma/client';

import { CourseAccessMeta, CourseAccessMetaType } from '../abstract/course-access-meta.abstract';
import { UserEmailParam } from '../abstract/notification-meta.abstract';
import { CategoryType, CourseType } from '../meta.schema';

export class CourseStudentInvited extends CourseAccessMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getEmailTitle(userParams: UserEmailParam): string {
        const { course, courses, categories } = this.meta as CourseAccessMetaType;
        const { locale } = userParams;

        // Если определен один курс
        if (course) {
            return {
                [LocaleEnum.ru]: 'Новый курс доступен для прохождения в вашем личном кабинете<br>',
                [LocaleEnum.en]: 'A new course is available for you to complete in your personal account<br>',
                [LocaleEnum.es]: 'Un nuevo curso está disponible para ti para completar en tu cuenta personal<br>',
                [LocaleEnum.kk]: 'Сізге жаңа курс ашық<br>',
                [LocaleEnum.uz]: 'Sizga yangi kurs ochiq<br>',
                [LocaleEnum.de]: 'Ein neuer Kurs ist für Sie in Ihrem persönlichen Konto verfügbar<br>',
                [LocaleEnum.fr]: 'Un nouveau cours est disponible pour vous dans votre compte personnel<br>',
                [LocaleEnum.it]: 'Un nuovo corso è disponibile per te nel tuo account personale<br>',
            }[locale];
        }

        // Если определены списки категорий или курсов
        if ((categories && categories.length > 0) || (courses && courses.length > 0)) {
            return {
                [LocaleEnum.ru]: 'Новые курсы доступны для прохождения в вашем личном кабинете<br>',
                [LocaleEnum.en]: 'New courses are available for you to complete in your personal account<br>',
                [LocaleEnum.es]: 'Nuevos cursos están disponibles para ti para completar en tu cuenta personal<br>',
                [LocaleEnum.kk]: 'Сізге жаңа курстар ашық<br>',
                [LocaleEnum.uz]: 'Sizga yangi kurslar ochiq<br>',
                [LocaleEnum.de]: 'Neue Kurse sind für Sie in Ihrem persönlichen Konto verfügbar<br>',
                [LocaleEnum.fr]: 'De nouveaux cours sont disponibles pour vous dans votre compte personnel<br>',
                [LocaleEnum.it]: 'Nuovi corsi sono disponibili per te nel tuo account personale<br>',
            }[locale];
        }

        // Fallback к одному курсу
        return {
            [LocaleEnum.ru]: 'Новый курс доступен для прохождения в вашем личном кабинете<br>',
            [LocaleEnum.en]: 'A new course is available for you to complete in your personal account<br>',
            [LocaleEnum.es]: 'Un nuevo curso está disponible para ti para completar en tu cuenta personal<br>',
            [LocaleEnum.kk]: 'Сізге жаңа курс ашық<br>',
            [LocaleEnum.uz]: 'Sizga yangi kurs ochiq<br>',
            [LocaleEnum.de]: 'Ein neuer Kurs ist für Sie in Ihrem persönlichen Konto verfügbar<br>',
            [LocaleEnum.fr]: 'Un nouveau cours est disponible pour vous dans votre compte personnel<br>',
            [LocaleEnum.it]: 'Un nuovo corso è disponibile per te nel tuo account personale<br>',
        }[locale];
    }

    private formatCategoriesList(categories: CategoryType[]): string {
        return categories
            .map((category) => `"${category.name}"`)
            .join(', ')
            .replace(/,([^,]*)$/, '.');
    }

    private formatCoursesList(courses: CourseType[]): string {
        return courses
            .map((course) => `"${course.name}"`)
            .join(', ')
            .replace(/,([^,]*)$/, '.');
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { course, courses, categories } = this.meta as CourseAccessMetaType;

        // Если определен один курс
        if (course) {
            return {
                [LocaleEnum.ru]: `Вам открыт доступ к курсу <b>"${course.name}"</b>.<br>`,
                [LocaleEnum.en]: `You have been invited to the course <b>"${course.name}"</b>.<br>`,
                [LocaleEnum.es]: `Se te ha concedido acceso al curso <b>"${course.name}"</b>.<br>`,
                [LocaleEnum.kk]: `Сізге <b>"${course.name}"</b> курсына кіру мүмкіндігі берілді.<br>`,
                [LocaleEnum.uz]: `Sizga <b>"${course.name}"</b> kursiga kirish huquqi berildi.<br>`,
                [LocaleEnum.de]: `Sie haben Zugriff auf den Kurs <b>"${course.name}"</b> erhalten.<br>`,
                [LocaleEnum.fr]: `Vous avez reçu un accès au cours <b>"${course.name}"</b>.<br>`,
                [LocaleEnum.it]: `Ti è stato concesso l'accesso al corso <b>"${course.name}"</b>.<br>`,
            }[locale];
        }

        // Если определены списки категорий и/или курсов
        const parts: string[] = [];

        if (categories && categories.length > 0) {
            const categoryNames = this.formatCategoriesList(categories);
            const categoryText = {
                [LocaleEnum.ru]: `Вам открыт доступ к курсам из категорий ${categoryNames}<br>`,
                [LocaleEnum.en]: `You have been granted access to courses from categories ${categoryNames}<br>`,
                [LocaleEnum.es]: `Se te ha concedido acceso a cursos de las categorías ${categoryNames}<br>`,
                [LocaleEnum.kk]: `Сізге ${categoryNames} категорияларынан курстарға кіру мүмкіндігі берілді<br>`,
                [LocaleEnum.uz]: `Sizga ${categoryNames} kategoriyalaridan kurslarga kirish huquqi berildi<br>`,
                [LocaleEnum.de]: `Sie haben Zugriff auf Kurse aus den Kategorien ${categoryNames} erhalten<br>`,
                [LocaleEnum.fr]: `Vous avez reçu un accès aux cours des catégories ${categoryNames}<br>`,
                [LocaleEnum.it]: `Ti è stato concesso l'accesso ai corsi delle categorie ${categoryNames}<br>`,
            }[locale];
            parts.push(categoryText);
        }

        if (courses && courses.length > 0) {
            const courseNames = this.formatCoursesList(courses);
            const courseText = {
                [LocaleEnum.ru]: `Вам открыт доступ к курсам ${courseNames}<br>`,
                [LocaleEnum.en]: `You have been granted access to courses ${courseNames}<br>`,
                [LocaleEnum.es]: `Se te ha concedido acceso a los cursos ${courseNames}<br>`,
                [LocaleEnum.kk]: `Сізге ${courseNames} курстарына кіру мүмкіндігі берілді<br>`,
                [LocaleEnum.uz]: `Sizga ${courseNames} kurslariga kirish huquqi berildi<br>`,
                [LocaleEnum.de]: `Sie haben Zugriff auf die Kurse ${courseNames} erhalten<br>`,
                [LocaleEnum.fr]: `Vous avez reçu un accès aux cours ${courseNames}<br>`,
                [LocaleEnum.it]: `Ti è stato concesso l'accesso ai corsi ${courseNames}<br>`,
            }[locale];
            parts.push(courseText);
        }

        if (parts.length > 0) {
            return parts.join(' ');
        }

        // возвращаем пустую строку, если ничего не определено
        return '';
    }

    public getEmailText(userParams: UserEmailParam): string {
        const { locale } = userParams;
        const messageText = this.getMessageText(locale);

        if (!messageText) {
            return '';
        }

        const loginPrompt = {
            [LocaleEnum.ru]: 'Чтобы начать обучение, войдите в аккаунт:<br>',
            [LocaleEnum.en]: 'To start learning, log in to your account:<br>',
            [LocaleEnum.es]: 'Para comenzar a aprender, inicia sesión en tu cuenta:<br>',
            [LocaleEnum.kk]: 'Оқуды бастау үшін, аккаунтқа кіріңіз:<br>',
            [LocaleEnum.uz]: "O'rganishni boshlash uchun, hisobingizga kiring:<br>",
            [LocaleEnum.de]: 'Um mit dem Lernen zu beginnen, melden Sie sich bei Ihrem Konto an:<br>',
            [LocaleEnum.fr]: 'Pour commencer à apprendre, connectez-vous à votre compte:<br>',
            [LocaleEnum.it]: 'Per iniziare ad imparare, accedi al tuo account:<br>',
        }[locale];

        return messageText + loginPrompt;
    }

    public getEmailButtonText(locale: LocaleEnum): string {
        return {
            [LocaleEnum.ru]: 'Войти в аккаунт',
            [LocaleEnum.en]: 'Log in to your account',
            [LocaleEnum.es]: 'Inicia sesión en tu cuenta',
            [LocaleEnum.kk]: 'Акаунтқа кіру',
            [LocaleEnum.uz]: 'Hisobingizga kiring',
            [LocaleEnum.de]: 'Anmelden bei Ihrem Konto',
            [LocaleEnum.fr]: 'Connectez-vous à votre compte',
            [LocaleEnum.it]: 'Accedi al tuo account',
        }[locale];
    }

    public getUrl(): string {
        return this.getCourseUrl();
    }
}
