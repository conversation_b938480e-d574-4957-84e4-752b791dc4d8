import { LocaleEnum } from '@prisma/client';

import {
    mockCreatedAt,
    mockMetaCategories,
    mockMetaCoursesAndCategories,
    mockMetaEmpty,
    mockMetaMultipleCourses,
    mockMetaSingleCourse,
    mockUserEmailParams,
} from './__fixtures__/course-student-invited.fixtures';
import { CourseStudentInvited } from './course-student-invited.meta';

describe('CourseStudentInvited', () => {
    describe('constructor', () => {
        it('should create instance with valid meta', () => {
            const instance = new CourseStudentInvited(mockMetaSingleCourse, mockCreatedAt);
            expect(instance).toBeInstanceOf(CourseStudentInvited);
        });
    });

    describe('getEmailTitle', () => {
        describe('single course scenario', () => {
            let instance: CourseStudentInvited;

            beforeEach(() => {
                instance = new CourseStudentInvited(mockMetaSingleCourse, mockCreatedAt);
            });

            it('should return correct title for Russian locale', () => {
                const result = instance.getEmailTitle(mockUserEmailParams[LocaleEnum.ru]);
                expect(result).toBe('Новый курс доступен для прохождения в вашем личном кабинете<br>');
            });

            it('should return correct title for English locale', () => {
                const result = instance.getEmailTitle(mockUserEmailParams[LocaleEnum.en]);
                expect(result).toBe('A new course is available for you to complete in your personal account<br>');
            });

            it('should return correct title for Spanish locale', () => {
                const result = instance.getEmailTitle(mockUserEmailParams[LocaleEnum.es]);
                expect(result).toBe('Un nuevo curso está disponible para ti para completar en tu cuenta personal<br>');
            });

            it('should return correct title for Kazakh locale', () => {
                const result = instance.getEmailTitle(mockUserEmailParams[LocaleEnum.kk]);
                expect(result).toBe('Сізге жаңа курс ашық<br>');
            });

            it('should return correct title for Uzbek locale', () => {
                const result = instance.getEmailTitle(mockUserEmailParams[LocaleEnum.uz]);
                expect(result).toBe('Sizga yangi kurs ochiq<br>');
            });

            it('should return correct title for German locale', () => {
                const result = instance.getEmailTitle(mockUserEmailParams[LocaleEnum.de]);
                expect(result).toBe('Ein neuer Kurs ist für Sie in Ihrem persönlichen Konto verfügbar<br>');
            });

            it('should return correct title for French locale', () => {
                const result = instance.getEmailTitle(mockUserEmailParams[LocaleEnum.fr]);
                expect(result).toBe('Un nouveau cours est disponible pour vous dans votre compte personnel<br>');
            });

            it('should return correct title for Italian locale', () => {
                const result = instance.getEmailTitle(mockUserEmailParams[LocaleEnum.it]);
                expect(result).toBe('Un nuovo corso è disponibile per te nel tuo account personale<br>');
            });
        });

        describe('multiple courses scenario', () => {
            let instance: CourseStudentInvited;

            beforeEach(() => {
                instance = new CourseStudentInvited(mockMetaMultipleCourses, mockCreatedAt);
            });

            it('should return plural title for Russian locale', () => {
                const result = instance.getEmailTitle(mockUserEmailParams[LocaleEnum.ru]);
                expect(result).toBe('Новые курсы доступны для прохождения в вашем личном кабинете<br>');
            });

            it('should return plural title for English locale', () => {
                const result = instance.getEmailTitle(mockUserEmailParams[LocaleEnum.en]);
                expect(result).toBe('New courses are available for you to complete in your personal account<br>');
            });
        });

        describe('categories scenario', () => {
            let instance: CourseStudentInvited;

            beforeEach(() => {
                instance = new CourseStudentInvited(mockMetaCategories, mockCreatedAt);
            });

            it('should return plural title for categories', () => {
                const result = instance.getEmailTitle(mockUserEmailParams[LocaleEnum.ru]);
                expect(result).toBe('Новые курсы доступны для прохождения в вашем личном кабинете<br>');
            });
        });

        describe('empty meta scenario', () => {
            let instance: CourseStudentInvited;

            beforeEach(() => {
                instance = new CourseStudentInvited(mockMetaEmpty, mockCreatedAt);
            });

            it('should return fallback single course title', () => {
                const result = instance.getEmailTitle(mockUserEmailParams[LocaleEnum.ru]);
                expect(result).toBe('Новый курс доступен для прохождения в вашем личном кабинете<br>');
            });
        });
    });

    describe('getMessageText', () => {
        describe('single course scenario', () => {
            let instance: CourseStudentInvited;

            beforeEach(() => {
                instance = new CourseStudentInvited(mockMetaSingleCourse, mockCreatedAt);
            });

            it('should return correct message for Russian locale', () => {
                const result = instance.getMessageText(LocaleEnum.ru);
                expect(result).toBe('Вам открыт доступ к курсу <b>"Test Course"</b>.<br>');
            });

            it('should return correct message for English locale', () => {
                const result = instance.getMessageText(LocaleEnum.en);
                expect(result).toBe('You have been invited to the course <b>"Test Course"</b>.<br>');
            });

            it('should use default locale when no locale provided', () => {
                const result = instance.getMessageText();
                expect(result).toBe('Вам открыт доступ к курсу <b>"Test Course"</b>.<br>');
            });
        });

        describe('multiple courses scenario', () => {
            let instance: CourseStudentInvited;

            beforeEach(() => {
                instance = new CourseStudentInvited(mockMetaMultipleCourses, mockCreatedAt);
            });

            it('should return message for multiple courses', () => {
                const result = instance.getMessageText(LocaleEnum.ru);
                expect(result).toBe('Вам открыт доступ к курсам "Test Course".<br>');
            });
        });

        describe('categories scenario', () => {
            let instance: CourseStudentInvited;

            beforeEach(() => {
                instance = new CourseStudentInvited(mockMetaCategories, mockCreatedAt);
            });

            it('should return message for categories', () => {
                const result = instance.getMessageText(LocaleEnum.ru);
                expect(result).toBe('Вам открыт доступ к курсам из категорий "Test Category".<br>');
            });
        });

        describe('courses and categories scenario', () => {
            let instance: CourseStudentInvited;

            beforeEach(() => {
                instance = new CourseStudentInvited(mockMetaCoursesAndCategories, mockCreatedAt);
            });

            it('should return combined message for courses and categories', () => {
                const result = instance.getMessageText(LocaleEnum.ru);
                expect(result).toContain('Вам открыт доступ к курсам из категорий "Test Category"<br>');
                expect(result).toContain('Вам открыт доступ к курсам "Test Course"<br>');
            });
        });

        describe('empty meta scenario', () => {
            let instance: CourseStudentInvited;

            beforeEach(() => {
                instance = new CourseStudentInvited(mockMetaEmpty, mockCreatedAt);
            });

            it('should return empty string for empty meta', () => {
                const result = instance.getMessageText(LocaleEnum.ru);
                expect(result).toBe('');
            });
        });
    });

    describe('getEmailText', () => {
        describe('single course scenario', () => {
            let instance: CourseStudentInvited;

            beforeEach(() => {
                instance = new CourseStudentInvited(mockMetaSingleCourse, mockCreatedAt);
            });

            it('should return email text with login prompt for Russian locale', () => {
                const result = instance.getEmailText(mockUserEmailParams[LocaleEnum.ru]);
                expect(result).toContain('Вам открыт доступ к курсу <b>"Test Course"</b>.<br>');
                expect(result).toContain('Чтобы начать обучение, войдите в аккаунт:<br>');
            });

            it('should return email text with login prompt for English locale', () => {
                const result = instance.getEmailText(mockUserEmailParams[LocaleEnum.en]);
                expect(result).toContain('You have been invited to the course <b>"Test Course"</b>.<br>');
                expect(result).toContain('To start learning, log in to your account:<br>');
            });
        });

        describe('empty meta scenario', () => {
            let instance: CourseStudentInvited;

            beforeEach(() => {
                instance = new CourseStudentInvited(mockMetaEmpty, mockCreatedAt);
            });

            it('should return empty string when message text is empty', () => {
                const result = instance.getEmailText(mockUserEmailParams[LocaleEnum.ru]);
                expect(result).toBe('');
            });
        });
    });

    describe('getEmailButtonText', () => {
        let instance: CourseStudentInvited;

        beforeEach(() => {
            instance = new CourseStudentInvited(mockMetaSingleCourse, mockCreatedAt);
        });

        it('should return correct button text for Russian locale', () => {
            const result = instance.getEmailButtonText(LocaleEnum.ru);
            expect(result).toBe('Войти в аккаунт');
        });

        it('should return correct button text for English locale', () => {
            const result = instance.getEmailButtonText(LocaleEnum.en);
            expect(result).toBe('Log in to your account');
        });

        it('should return correct button text for Spanish locale', () => {
            const result = instance.getEmailButtonText(LocaleEnum.es);
            expect(result).toBe('Inicia sesión en tu cuenta');
        });

        it('should return correct button text for all supported locales', () => {
            const locales = Object.values(LocaleEnum);
            locales.forEach((locale) => {
                const result = instance.getEmailButtonText(locale);
                expect(result).toBeTruthy();
                expect(typeof result).toBe('string');
            });
        });
    });

    describe('getUrl', () => {
        let instance: CourseStudentInvited;

        beforeEach(() => {
            instance = new CourseStudentInvited(mockMetaSingleCourse, mockCreatedAt);
        });

        it('should return course URL', () => {
            const result = instance.getUrl();
            expect(result).toBe(`${mockMetaSingleCourse.domain}/course/${mockMetaSingleCourse.course.id}`);
        });
    });

    describe('formatCategoriesList', () => {
        let instance: CourseStudentInvited;

        beforeEach(() => {
            instance = new CourseStudentInvited(mockMetaCategories, mockCreatedAt);
        });

        it('should format categories list correctly', () => {
            // Тестируем через getMessageText, так как formatCategoriesList - приватный метод
            const result = instance.getMessageText(LocaleEnum.ru);
            expect(result).toContain('"Test Category"');
        });
    });

    describe('formatCoursesList', () => {
        let instance: CourseStudentInvited;

        beforeEach(() => {
            instance = new CourseStudentInvited(mockMetaMultipleCourses, mockCreatedAt);
        });

        it('should format courses list correctly', () => {
            // Тестируем через getMessageText, так как formatCoursesList - приватный метод
            const result = instance.getMessageText(LocaleEnum.ru);
            expect(result).toContain('"Test Course"');
        });
    });
});
