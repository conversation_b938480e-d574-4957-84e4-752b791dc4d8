import { LocaleEnum } from '@prisma/client';

import { CourseAccessMetaType } from '../../abstract/course-access-meta.abstract';
import { UserEmailParam } from '../../abstract/notification-meta.abstract';
import { CategoryType, CourseType } from '../../meta.schema';

export const mockCourse: CourseType = {
    id: 1,
    uuid: '123e4567-e89b-12d3-a456-************',
    name: 'Test Course',
};

export const mockCourse2: CourseType = {
    id: 2,
    uuid: '123e4567-e89b-12d3-a456-************',
    name: 'Second Test Course',
};

export const mockCategory: CategoryType = {
    id: 1,
    uuid: '123e4567-e89b-12d3-a456-************',
    name: 'Test Category',
};

export const mockCategory2: CategoryType = {
    id: 2,
    uuid: '123e4567-e89b-12d3-a456-************',
    name: 'Second Test Category',
};

export const mockWhiteLabel = {
    primaryColor: '#17b198',
    hideMobileAppLinks: true,
    isWhiteLabel: true,
    schoolLogoUrl: null,
    textOnly: false,
};

export const mockDomain = 'test.example.com';
export const mockUrl = `https://${mockDomain}/course/1`;

// Мета с одним курсом
export const mockMetaSingleCourse: CourseAccessMetaType = {
    whiteLabel: mockWhiteLabel,
    domain: mockDomain,
    url: mockUrl,
    course: mockCourse,
};

// Мета с несколькими курсами
export const mockMetaMultipleCourses: CourseAccessMetaType = {
    whiteLabel: mockWhiteLabel,
    domain: mockDomain,
    url: mockUrl,
    courses: [mockCourse, mockCourse2],
};

// Мета с категориями
export const mockMetaCategories: CourseAccessMetaType = {
    whiteLabel: mockWhiteLabel,
    domain: mockDomain,
    url: mockUrl,
    categories: [mockCategory, mockCategory2],
};

// Мета с курсами и категориями
export const mockMetaCoursesAndCategories: CourseAccessMetaType = {
    whiteLabel: mockWhiteLabel,
    domain: mockDomain,
    url: mockUrl,
    courses: [mockCourse],
    categories: [mockCategory],
};

// Пустая мета (для fallback тестов)
export const mockMetaEmpty: CourseAccessMetaType = {
    whiteLabel: mockWhiteLabel,
    domain: mockDomain,
    url: mockUrl,
};

export const mockUserEmailParams: Record<LocaleEnum, UserEmailParam> = {
    [LocaleEnum.ru]: {
        locale: LocaleEnum.ru,
        unSubscribeUrl: 'https://test.example.com/unsubscribe',
        timezone: 'Europe/Moscow',
        timezoneAbbr: 'MSK',
    },
    [LocaleEnum.en]: {
        locale: LocaleEnum.en,
        unSubscribeUrl: 'https://test.example.com/unsubscribe',
        timezone: 'America/New_York',
        timezoneAbbr: 'EST',
    },
    [LocaleEnum.es]: {
        locale: LocaleEnum.es,
        unSubscribeUrl: 'https://test.example.com/unsubscribe',
        timezone: 'Europe/Madrid',
        timezoneAbbr: 'CET',
    },
    [LocaleEnum.kk]: {
        locale: LocaleEnum.kk,
        unSubscribeUrl: 'https://test.example.com/unsubscribe',
        timezone: 'Asia/Almaty',
        timezoneAbbr: 'ALMT',
    },
    [LocaleEnum.uz]: {
        locale: LocaleEnum.uz,
        unSubscribeUrl: 'https://test.example.com/unsubscribe',
        timezone: 'Asia/Tashkent',
        timezoneAbbr: 'UZT',
    },
    [LocaleEnum.de]: {
        locale: LocaleEnum.de,
        unSubscribeUrl: 'https://test.example.com/unsubscribe',
        timezone: 'Europe/Berlin',
        timezoneAbbr: 'CET',
    },
    [LocaleEnum.fr]: {
        locale: LocaleEnum.fr,
        unSubscribeUrl: 'https://test.example.com/unsubscribe',
        timezone: 'Europe/Paris',
        timezoneAbbr: 'CET',
    },
    [LocaleEnum.it]: {
        locale: LocaleEnum.it,
        unSubscribeUrl: 'https://test.example.com/unsubscribe',
        timezone: 'Europe/Rome',
        timezoneAbbr: 'CET',
    },
};

export const mockCreatedAt = new Date('2023-01-01T12:00:00Z');
