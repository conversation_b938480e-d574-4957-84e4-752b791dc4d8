import { LocaleEnum } from '@prisma/client';

import { CourseStudentInviteAndRegisterMeta } from './course-student-invited-registration.meta';
import {
    mockRegistrationCreatedAt,
    mockRegistrationMetaEmpty,
    mockRegistrationMetaMultipleCourses,
    mockRegistrationMetaSingleCourse,
    mockRegistrationUserEmailParams,
} from './__fixtures__/course-student-invited-registration.fixtures';

describe('CourseStudentInviteAndRegisterMeta', () => {
    describe('constructor', () => {
        it('should create instance with valid meta', () => {
            const instance = new CourseStudentInviteAndRegisterMeta(
                mockRegistrationMetaSingleCourse,
                mockRegistrationCreatedAt,
            );
            expect(instance).toBeInstanceOf(CourseStudentInviteAndRegisterMeta);
        });

        it('should inherit from CourseStudentInvited', () => {
            const instance = new CourseStudentInviteAndRegisterMeta(
                mockRegistrationMetaSingleCourse,
                mockRegistrationCreatedAt,
            );
            // Проверяем, что наследуется метод getMessageText от родительского класса
            expect(typeof instance.getMessageText).toBe('function');
        });
    });

    describe('getEmailTitle', () => {
        let instance: CourseStudentInviteAndRegisterMeta;

        beforeEach(() => {
            instance = new CourseStudentInviteAndRegisterMeta(
                mockRegistrationMetaSingleCourse,
                mockRegistrationCreatedAt,
            );
        });

        it('should return correct title for Russian locale', () => {
            const result = instance.getEmailTitle(mockRegistrationUserEmailParams[LocaleEnum.ru]);
            expect(result).toBe('Осталось установить пароль и войти в аккаунт<br>');
        });

        it('should return correct title for English locale', () => {
            const result = instance.getEmailTitle(mockRegistrationUserEmailParams[LocaleEnum.en]);
            expect(result).toBe('Set a password and log in to your account<br>');
        });

        it('should return correct title for Spanish locale', () => {
            const result = instance.getEmailTitle(mockRegistrationUserEmailParams[LocaleEnum.es]);
            expect(result).toBe('Establece una contraseña e inicia sesión en tu cuenta<br>');
        });

        it('should return correct title for Kazakh locale', () => {
            const result = instance.getEmailTitle(mockRegistrationUserEmailParams[LocaleEnum.kk]);
            expect(result).toBe('Құпия сөзді орнату керек және акаунтқа кіру<br>');
        });

        it('should return correct title for Uzbek locale', () => {
            const result = instance.getEmailTitle(mockRegistrationUserEmailParams[LocaleEnum.uz]);
            expect(result).toBe('Parol o`rnatish va hisobingizga kiring<br>');
        });

        it('should return correct title for German locale', () => {
            const result = instance.getEmailTitle(mockRegistrationUserEmailParams[LocaleEnum.de]);
            expect(result).toBe('Legen Sie ein Passwort fest und melden Sie sich bei Ihrem Konto an<br>');
        });

        it('should return correct title for French locale', () => {
            const result = instance.getEmailTitle(mockRegistrationUserEmailParams[LocaleEnum.fr]);
            expect(result).toBe('Définissez un mot de passe et connectez-vous à votre compte<br>');
        });

        it('should return correct title for Italian locale', () => {
            const result = instance.getEmailTitle(mockRegistrationUserEmailParams[LocaleEnum.it]);
            expect(result).toBe('Imposta una password e accedi al tuo account<br>');
        });

        it('should override parent getEmailTitle method', () => {
            // Проверяем, что метод переопределен и возвращает другой результат, чем родительский класс
            const result = instance.getEmailTitle(mockRegistrationUserEmailParams[LocaleEnum.ru]);
            expect(result).not.toBe('Новый курс доступен для прохождения в вашем личном кабинете<br>');
        });
    });

    describe('getEmailText', () => {
        describe('single course scenario', () => {
            let instance: CourseStudentInviteAndRegisterMeta;

            beforeEach(() => {
                instance = new CourseStudentInviteAndRegisterMeta(
                    mockRegistrationMetaSingleCourse,
                    mockRegistrationCreatedAt,
                );
            });

            it('should return email text with registration prompt for Russian locale', () => {
                const result = instance.getEmailText(mockRegistrationUserEmailParams[LocaleEnum.ru]);
                expect(result).toContain('Вам открыт доступ к курсу <b>"Registration Test Course"</b>.<br>');
                expect(result).toContain(
                    'Чтобы войти в аккаунт и начать обучение, нажмите на кнопку ниже и установите пароль<br>',
                );
            });

            it('should return email text with registration prompt for English locale', () => {
                const result = instance.getEmailText(mockRegistrationUserEmailParams[LocaleEnum.en]);
                expect(result).toContain('You have been invited to the course <b>"Registration Test Course"</b>.<br>');
                expect(result).toContain(
                    'To log in to your account and start learning, click the button below and set a password<br>',
                );
            });

            it('should return email text with registration prompt for Spanish locale', () => {
                const result = instance.getEmailText(mockRegistrationUserEmailParams[LocaleEnum.es]);
                expect(result).toContain(
                    'Para iniciar sesión en tu cuenta y comenzar a aprender, haz clic en el botón de abajo y establece una contraseña<br>',
                );
            });

            it('should return email text with registration prompt for Kazakh locale', () => {
                const result = instance.getEmailText(mockRegistrationUserEmailParams[LocaleEnum.kk]);
                expect(result).toContain(
                    'Аккаунтқа кіру және оқуды бастау үшін, төмендегі батырманы басып, құпия сөзді орнатыңыз<br>',
                );
            });

            it('should return email text with registration prompt for Uzbek locale', () => {
                const result = instance.getEmailText(mockRegistrationUserEmailParams[LocaleEnum.uz]);
                expect(result).toContain(
                    "Hisobingizga kirish va o'rganishni boshlash uchun, quyidagi tugmani bosing va parol o'rnating<br>",
                );
            });

            it('should return email text with registration prompt for German locale', () => {
                const result = instance.getEmailText(mockRegistrationUserEmailParams[LocaleEnum.de]);
                expect(result).toContain(
                    'Um sich bei Ihrem Konto anzumelden und mit dem Lernen zu beginnen, klicken Sie auf die Schaltfläche unten und legen Sie ein Passwort fest<br>',
                );
            });

            it('should return email text with registration prompt for French locale', () => {
                const result = instance.getEmailText(mockRegistrationUserEmailParams[LocaleEnum.fr]);
                expect(result).toContain(
                    'Pour vous connecter à votre compte et commencer à apprendre, cliquez sur le bouton ci-dessous et définissez un mot de passe<br>',
                );
            });

            it('should return email text with registration prompt for Italian locale', () => {
                const result = instance.getEmailText(mockRegistrationUserEmailParams[LocaleEnum.it]);
                expect(result).toContain(
                    'Per accedere al tuo account e iniziare ad imparare, clicca sul pulsante qui sotto e imposta una password<br>',
                );
            });
        });

        describe('empty meta scenario', () => {
            let instance: CourseStudentInviteAndRegisterMeta;

            beforeEach(() => {
                instance = new CourseStudentInviteAndRegisterMeta(mockRegistrationMetaEmpty, mockRegistrationCreatedAt);
            });

            it('should return empty string when message text is empty', () => {
                const result = instance.getEmailText(mockRegistrationUserEmailParams[LocaleEnum.ru]);
                expect(result).toBe('');
            });
        });
    });

    describe('getEmailButtonText', () => {
        let instance: CourseStudentInviteAndRegisterMeta;

        beforeEach(() => {
            instance = new CourseStudentInviteAndRegisterMeta(
                mockRegistrationMetaSingleCourse,
                mockRegistrationCreatedAt,
            );
        });

        it('should return same button text as parent class for Russian locale', () => {
            const result = instance.getEmailButtonText(LocaleEnum.ru);
            expect(result).toBe('Войти в аккаунт');
        });

        it('should return same button text as parent class for English locale', () => {
            const result = instance.getEmailButtonText(LocaleEnum.en);
            expect(result).toBe('Log in to your account');
        });

        it('should return correct button text for all supported locales', () => {
            const locales = Object.values(LocaleEnum);
            locales.forEach((locale) => {
                const result = instance.getEmailButtonText(locale);
                expect(result).toBeTruthy();
                expect(typeof result).toBe('string');
            });
        });
    });

    describe('getUrl', () => {
        let instance: CourseStudentInviteAndRegisterMeta;

        beforeEach(() => {
            instance = new CourseStudentInviteAndRegisterMeta(
                mockRegistrationMetaSingleCourse,
                mockRegistrationCreatedAt,
            );
        });

        it('should return course URL same as parent class', () => {
            const result = instance.getUrl();
            expect(result).toBe(
                `${mockRegistrationMetaSingleCourse.domain}/course/${mockRegistrationMetaSingleCourse.course.id}`,
            );
        });
    });

    describe('inheritance behavior', () => {
        let instance: CourseStudentInviteAndRegisterMeta;

        beforeEach(() => {
            instance = new CourseStudentInviteAndRegisterMeta(
                mockRegistrationMetaMultipleCourses,
                mockRegistrationCreatedAt,
            );
        });

        it('should inherit getMessageText from parent class', () => {
            const result = instance.getMessageText(LocaleEnum.ru);
            expect(result).toContain('Вам открыт доступ к курсам');
            expect(result).toContain('"Registration Test Course"');
        });

        it('should use inherited formatCoursesList method', () => {
            const result = instance.getMessageText(LocaleEnum.ru);
            expect(result).toContain('"Registration Test Course"');
        });
    });
});
