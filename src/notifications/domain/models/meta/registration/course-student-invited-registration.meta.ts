import { LocaleEnum } from '@prisma/client';

import { UserEmailParam } from '../abstract/notification-meta.abstract';
import { CourseStudentInvited } from '../courses/course-student-invited.meta';

export class CourseStudentInviteAndRegisterMeta extends CourseStudentInvited {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getEmailTitle(userParams: UserEmailParam): string {
        return {
            [LocaleEnum.ru]: 'Осталось установить пароль и войти в аккаунт<br>',
            [LocaleEnum.en]: 'Set a password and log in to your account<br>',
            [LocaleEnum.es]: 'Establece una contraseña e inicia sesión en tu cuenta<br>',
            [LocaleEnum.kk]: 'Құпия сөзді орнату керек және акаунтқа кіру<br>',
            [LocaleEnum.uz]: '<PERSON><PERSON> o`rnatish va hisobingizga kiring<br>',
            [LocaleEnum.de]: 'Legen Sie ein Passwort fest und melden Sie sich bei Ihrem Konto an<br>',
            [LocaleEnum.fr]: 'Définissez un mot de passe et connectez-vous à votre compte<br>',
            [LocaleEnum.it]: 'Imposta una password e accedi al tuo account<br>',
        }[userParams.locale];
    }

    public getEmailText(userParams: UserEmailParam): string {
        const { locale } = userParams;
        const messageText = this.getMessageText(locale);

        if (!messageText) {
            return '';
        }

        const loginPrompt = {
            [LocaleEnum.ru]: 'Чтобы войти в аккаунт и начать обучение, нажмите на кнопку ниже и установите пароль<br>',
            [LocaleEnum.en]:
                'To log in to your account and start learning, click the button below and set a password<br>',
            [LocaleEnum.es]:
                'Para iniciar sesión en tu cuenta y comenzar a aprender, haz clic en el botón de abajo y establece una contraseña<br>',
            [LocaleEnum.kk]:
                'Аккаунтқа кіру және оқуды бастау үшін, төмендегі батырманы басып, құпия сөзді орнатыңыз<br>',
            [LocaleEnum.uz]:
                "Hisobingizga kirish va o'rganishni boshlash uchun, quyidagi tugmani bosing va parol o'rnating<br>",
            [LocaleEnum.de]:
                'Um sich bei Ihrem Konto anzumelden und mit dem Lernen zu beginnen, klicken Sie auf die Schaltfläche unten und legen Sie ein Passwort fest<br>',
            [LocaleEnum.fr]:
                'Pour vous connecter à votre compte et commencer à apprendre, cliquez sur le bouton ci-dessous et définissez un mot de passe<br>',
            [LocaleEnum.it]:
                'Per accedere al tuo account e iniziare ad imparare, clicca sul pulsante qui sotto e imposta una password<br>',
        }[locale];

        return messageText + loginPrompt;
    }

    public getEmailButtonText(locale: LocaleEnum): string {
        return {
            [LocaleEnum.ru]: 'Войти в аккаунт',
            [LocaleEnum.en]: 'Log in to your account',
            [LocaleEnum.es]: 'Inicia sesión en tu cuenta',
            [LocaleEnum.kk]: 'Акаунтқа кіру',
            [LocaleEnum.uz]: 'Hisobingizga kiring',
            [LocaleEnum.de]: 'Anmelden bei Ihrem Konto',
            [LocaleEnum.fr]: 'Connectez-vous à votre compte',
            [LocaleEnum.it]: 'Accedi al tuo account',
        }[locale];
    }

    public getUrl(): string {
        return this.getCourseUrl();
    }
}
