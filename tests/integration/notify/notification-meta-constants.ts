import { ActionEnum } from '@prisma/client';

import { FeatureCodeEnum } from '../../../src/notifications/domain/models/meta/meta.schema';

const domain = 'dev.sksp.site';

const host = `https://${domain}`;

const course = {
    id: 78,
    name: 'Курс молодого сурка',
    uuid: '421c66b4-8f92-4c43-aab7-c4d571dff99b',
};

const lesson = {
    id: 422,
    name: 'Вводное занятие',
    uuid: '88701a3d-73b9-4749-8329-d56f7078e6b3',
};

const step = {
    id: 102,
    uuid: 'ba5f8b01-b8d9-4aa6-aa45-676c98cb9875',
    name: 'Модуль 2',
};

const group = {
    id: 132,
    uuid: '9da94068-9dfe-427d-93e6-cb35a476cf6c',
    name: 'Первая группа',
    limit: 7,
};

const student = {
    id: 2261,
    uuid: '51d299c3-c389-427c-a354-f23152c60990',
    name: 'Вася Куролесов',
    avatar: null,
};

const webinar = {
    startAt: new Date('2025-02-04').valueOf(),
    // url 2020-01-01T13:00:00.000Z
};

const homeworkUser = {
    id: 86,
    uuid: '6954d434-afa6-48b8-aaa3-80f951352634',
};

const teacher = {
    id: 2238,
    uuid: 'd59f6230-8659-440a-9b06-dde0b687934c',
    name: 'Егор Кридман',
    avatar: null,
};

const whiteLabel = {
    primaryColor: '#17b198',
    hideMobileAppLinks: true,
    isWhiteLabel: true,
    schoolLogoUrl: null,
    textOnly: false,
};

const requisites = {
    createAt: 1731079924000,
};

const partner = {
    id: 5,
    uuid: 'b1f02c5a-1644-484f-859b-25459c9bfc22',
    level: {
        uuid: '24cb639a-81a9-4cb4-aa6d-a8164006876d',
        slug: 'bronze',
        name: 'Бронзовый',
    },
    totalEarned: 9823,
    withdraw: 8323,
};

const transaction = {
    id: 15,
    amount: 2249,
    createAt: 1731939096000,
};

const referralPayment = {
    paymentDate: 1730978283000,
    sum: 14990,
    commission: 15,
    commissionSum: 2249,
    school: {
        id: 62,
        uuid: '6c6c0047-2230-4c15-a8e4-7471311bd609',
        name: 'Название проекта',
        avatar: null,
        url: 'https://1b42c1.dev1.sksp.site',
        tariffName: 'Бизнес',
    },
};

const school = {
    id: 52,
    name: 'Школа ниндзя',
    uuid: 'f3ca03a7-4634-4de0-a103-cd2cbca956ab',
    avatar: null,
};

const promoCode = {
    id: 13,
    uuid: 'bd66ae61-3c29-47dc-89a3-90e272844bef',
    code: '2f4_2h32+4',
    activeFrom: null,
    activeTo: 1732093080000,
};

const feature = {
    id: 1,
    name: 'Example Feature',
    code: FeatureCodeEnum.STUDENT_LIMIT,
    limit: 100,
    usage_limit: 50,
    usage: 75,
};

export const META = {
    // COURSES

    [ActionEnum.ACTION_COURSE_ACCESS_STUDENT_ACCESS_END]: {
        meta: {
            whiteLabel,
            course,
            domain,
        },
        text: `Истекло время доступа к курсу <b>"${course.name}"</b>`,
    },
    [ActionEnum.ACTION_COURSE_PUBLISHED_NEW_LESSON]: {
        meta: {
            whiteLabel,
            course,
            lesson,
            domain,
            url: `${host}/course/${course.id}/${lesson.id}`,
        },
        text: `В курсе <b>"${course.name}"</b> появилось новое занятие <b>"${lesson.name}"</b>`,
    },
    [ActionEnum.ACTION_COURSE_ACCESS_STUDENT_ACCESS_END_3_DAYS]: {
        meta: {
            whiteLabel,
            course,
            domain,
            url: `${host}/course/${course.id}`,
        },
        text: `Доступ к курсу <b>"${course.name}"</b> закончится <b>через 3 дня</b>`,
    },
    [ActionEnum.ACTION_COURSE_ACCESS_STUDENT_NEW_LESSON]: {
        meta: {
            whiteLabel,
            course,
            lesson,
            domain,
            url: `${host}/course/${course.id}`,
        },
        text: `Открыт доступ к занятию <b>"${lesson.name}"</b> в курсе <b>"${course.name}"</b>`,
    },
    [ActionEnum.ACTION_COURSE_ACCESS_STUDENT_NEW_STEP]: {
        meta: {
            whiteLabel,
            course,
            step,
            domain,
        },
        text: `Открыт доступ к модулю <b>"${step.name}"</b> в курсе <b>"${course.name}"</b>`,
    },
    [ActionEnum.ACTION_COURSE_ACCESS_STUDENT_INVITE]: {
        meta: {
            whiteLabel,
            course,
            domain,
            url: `${host}/course/${course.id}`,
        },
        text: `Вам открыт доступ к курсу <b>"${course.name}"</b>`,
    },
    [ActionEnum.ACTION_COURSE_ACCESS_STUDENT_INVITE_AND_REGISTER]: {
        meta: {
            whiteLabel,
            course,
            domain,
            url: `${host}/course/${course.id}`,
        },
        text: `Вам открыт доступ к курсу <b>"${course.name}"</b>`,
    },
    [ActionEnum.ACTION_COURSE_ACCESS_STUDENT_EXPEL]: {
        meta: {
            whiteLabel,
            course,
            school,
            domain,
        },
        text: `Доступ к курсу <b>"${course.name}"</b> заблокирован администратором`,
    },

    // COURSE_ACCESS_GROUP_STUDENT_LIMIT_OVER

    [ActionEnum.ACTION_COURSE_ACCESS_GROUP_STUDENT_LIMIT_OVER]: {
        meta: {
            whiteLabel,
            course,
            group, // использовали только name и id
            domain,
            url: `${host}/students/courses/${course.id}/${group.id}`,
        },
        text: `Закончились места в группе <b>"${group.name}"</b> курса <b>"${course.name}"</b>`,
    },

    // COURSE_STUDENT_FINISHED

    [ActionEnum.ACTION_COURSE_STUDENT_FINISH_COURSE]: {
        meta: {
            whiteLabel,
            course,
            student,
            domain,
            url: `${host}/students/courses/${course.id}`,
        },
        text: `Ученик <b>"${student.name}"</b> завершил курс <b>"${course.name}"</b>`,
    },

    // WEBINARS

    [ActionEnum.ACTION_COURSE_UPCOMING_WEBINAR_STUDENT]: {
        meta: {
            whiteLabel,
            course,
            lesson,
            webinar,
            domain,
            url: `${host}/course/${course.id}/${lesson.id}`,
        },
        text: `До начала вебинара <b>"${lesson.name}"</b> осталось 4 минуты`, // TODO: высчитать по Webinar.startAt
    },
    [ActionEnum.ACTION_COURSE_WEBINAR_START_STUDENT]: {
        meta: {
            whiteLabel,
            course,
            lesson,
            webinar,
            url: `${host}/course/${course.id}/${lesson.id}`,
            domain,
        },
        text: `Вебинар <b>"${lesson.name}"</b> начался`,
    },
    [ActionEnum.ACTION_COURSE_UPCOMING_WEBINAR_TEACHER]: {
        meta: {
            whiteLabel,
            course,
            lesson,
            webinar,
            url: `${host}/course/${course.id}/constructor/${lesson.uuid}`,
            domain,
        },
        text: `До начала вебинара <b>"${lesson.name}"</b> осталось 4 минуты`, // TODO: высчитать по Webinar.startAt
    },

    // HOMEWORKS

    [ActionEnum.ACTION_HOMEWORK_STUDENT_SENT]: {
        meta: {
            whiteLabel,
            lesson, // не использовалось
            student, // не использовалось
            homeworkUser,
            url: `${host}/school/homeworks?uuid=${homeworkUser.uuid}`,
            domain,
        },
        text: 'Ученик прислал ответ на задание',
    },
    [ActionEnum.ACTION_HOMEWORK_TEACHER_SENT]: {
        meta: {
            whiteLabel,
            teacher, // не использовалось
            course, // не использовалось
            lesson,
            homeworkUser, // не использовалось
            domain,
        },
        text: `Новый ответ преподавателя по заданию <b>"${lesson.name}"</b>`,
    },
    [ActionEnum.ACTION_HOMEWORKS_STUDENTS_SENT_CRON]: {
        meta: {
            whiteLabel,
            domain,
        },
        text: 'Ученики прислали ответы на задания',
    },
    [ActionEnum.ACTION_HOMEWORKS_TEACHER_SENT_CRON]: {
        meta: {
            whiteLabel,
            domain,
        },
        text: 'Ваши домашние работы проверены',
    },

    // DEADLINES

    [ActionEnum.ACTION_DEADLINE_UPCOMING_1_DAY]: {
        meta: {
            whiteLabel,
            course, // не приходит, но нужно для построения url
            lesson,
            domain,
            url: `${host}/course/${course.id}/${lesson.id}`,
        },
        text: `Остался 1 день на выполнение теста <b>${lesson.name}</b>`, // тест или задание?
    },
    [ActionEnum.ACTION_DEADLINE_UPCOMING_3_DAYS]: {
        meta: {
            whiteLabel,
            course, // не приходит, но нужно для построения url
            lesson,
            domain,
            url: `${host}/course/${course.id}/${lesson.id}`,
        },
        text: `Остался 3 дня на прохождение задания <b>${lesson.name}</b>`,
    },
    [ActionEnum.ACTION_DEADLINE_EXPIRED]: {
        meta: {
            whiteLabel,
            course, // не приходит, но нужно для построения url
            lesson,
            domain,
            url: `${host}/course/${course.id}/${lesson.id}`,
        },
        text: `Вы не успели выполнить задание <b>${lesson.name}</b>`,
    },

    // AFFILIATE_UPDATES

    [ActionEnum.ACTION_AFFILIATE_REQUISITES_ACCEPTED]: {
        meta: {
            requisites,
            partner,
            url: `${host}/partner`,
        },
        text: 'Верификация пройдена',
    },
    [ActionEnum.ACTION_AFFILIATE_REQUISITES_DECLINED]: {
        meta: {
            requisites,
            partner,
            url: `${host}/partner`,
        },
        text: 'Верификация не пройдена',
    },
    [ActionEnum.ACTION_AFFILIATE_WITHDRAW_ACCEPTED]: {
        meta: {
            transaction,
            partner,
            url: `${host}/partner`,
        },
        text: 'Вывод средств партнера подтвержден',
    },
    [ActionEnum.ACTION_AFFILIATE_WITHDRAW_DECLINED]: {
        meta: {
            transaction,
            partner,
            url: `${host}/partner`,
        },
        text: 'Вывод средств партнера отклонен',
    },
    [ActionEnum.ACTION_AFFILIATE_LEVEL_UPDATE]: {
        meta: {
            partner,
            url: `${host}/partner`,
        },
        text: `Ваш статус партнерской программе - <b>"${partner.level.name}"</b>`,
    },
    [ActionEnum.ACTION_AFFILIATE_REFERRAL_PAYMENT]: {
        meta: {
            referralPayment: referralPayment,
            partner,
            url: `${host}/partner`,
        },
        text: `Новая оплата по вашей партнерской ссылке`,
    },

    // SCHOOL_SETTINGS

    [ActionEnum.ACTION_SCHOOL_SETTINGS_DISCOUNT_EXPIRED]: {
        meta: {
            whiteLabel,
            promoCode,
            domain,
            url: `${host}/school/settings/payment-gateways`,
        },
        text: 'Истек срок действия промокода',
    },

    // CHAT

    [ActionEnum.ACTION_CHAT_STUDENT_WAIT_ANSWER_24_HOUR]: {
        meta: {
            whiteLabel,
            student,
        },
        text: `${student.name} ждет ответа в чате более 24 часов`,
    },

    // BILLING

    [ActionEnum.ACTION_BILLING_STUDENT_PAID_COURSE]: {
        meta: {
            whiteLabel,
            course,
            student,
            group,
            domain,
            url: `${host}/balance`,
        },
        text: `Ученик <b>"${student.name}"</b> оплатил курс <b>"${course.name}"</b>`,
    },

    // WITHDRAWAL

    [ActionEnum.ACTION_BILLING_WITHDRAW_ACCEPTED]: {
        meta: {
            whiteLabel,
            transaction,
            school,
            domain,
            url: `${host}/balance`,
        },
        text: 'Запрос на вывод средств подтвержден',
    },
    [ActionEnum.ACTION_BILLING_WITHDRAW_DECLINE]: {
        meta: {
            whiteLabel,
            transaction,
            school,
            domain,
            url: `${host}/balance`,
        },
        text: 'Запрос на вывод средств отклонен',
    },
    [ActionEnum.ACTION_BILLING_SCHOOL_REQUISITES_ACCEPTED]: {
        meta: {
            whiteLabel,
            requisites,
            school,
            domain,
            url: `${host}/balance`,
        },
        text: 'Запрос на вывод средств отклонен',
    },
    [ActionEnum.ACTION_BILLING_SCHOOL_REQUISITES_DECLINED]: {
        meta: {
            whiteLabel,
            requisites,
            school,
            domain,
            url: `${host}/balance`,
        },
        text: 'Верификация не пройдена',
    },

    // TARIFFS

    [ActionEnum.ACTION_TARIFF_FEATURE_LIMIT_EXCEEDED]: {
        meta: {
            feature,
            url: `${host}/tariff/plan`, // TODO ROUTE_TARIFF_PLAN
        },
        text: `Кол-во учеников в этом месяце заполнено на <b>${(feature.usage_limit / feature.limit) * 100}%</b>`,
    },

    // SUBSCRIPTION

    [ActionEnum.ACTION_TARIFF_SUBSCRIPTION_EXPIRED]: {
        meta: {
            whiteLabel,
            domain,
            url: `${host}/plans`,
        },
        text: 'Подписка истекла. Ученикам заблокирован доступ к обучению',
    },
    [ActionEnum.ACTION_TARIFF_SUBSCRIPTION_EXPIRATION_IN_7_DAYS]: {
        meta: {
            whiteLabel,
            domain,
            url: `${host}/plans`,
        },
        text: 'Осталось 7 дней до окончания подписки',
    },
    [ActionEnum.ACTION_TARIFF_SUBSCRIPTION_EXPIRATION_IN_3_DAYS]: {
        meta: {
            whiteLabel,
            domain,
            url: `${host}/plans`,
        },
        text: 'Осталось 3 дня до окончания подписки',
    },
    [ActionEnum.ACTION_TARIFF_SUBSCRIPTION_EXPIRATION_IN_1_DAY]: {
        meta: {
            whiteLabel,
            domain,
            url: `${host}/plans`,
        },
        text: 'Сегодня подписка на Skillspace закончится и ученики потеряют доступ',
    },
    [ActionEnum.ACTION_TARIFF_SUBSCRIPTION_RECURRENT_PAYMENT_FAILURE]: {
        meta: {
            url: `${host}/tariff/plan`,
        },
        text: 'Ошибка автоматического продления подписки.',
    }, // TODO ROUTE_TARIFF_PLAN
};
