import 'reflect-metadata';

import { LocaleEnum, Prisma } from '@prisma/client';

import * as userSettingsData from './__data__/notifications-settings.data.json';
import { TestAction } from './settings-constants';
import { createMessageV2, getUsers } from './test.utils';
import {
    emailSpy,
    notificationConsumer,
    notificationsResolver,
    prisma,
    telegramSpy,
    userSession1,
    userSession2,
    wsSpy,
} from './test-setup';

describe('Интеграционные тесты для действий приглашения студентов к курсам', () => {
    beforeAll(async () => {
        await prisma.notificationsSettings.createMany({
            data: userSettingsData as Prisma.NotificationsSettingsCreateInput[],
        });
    });

    beforeEach(async () => {
        jest.clearAllMocks();
    });

    afterAll(async () => {
        await prisma.notificationsSettings.deleteMany();
    });

    afterEach(async () => {
        await prisma.userNotification.deleteMany();
        await prisma.notification.deleteMany();
    });

    it('Резолвер уведомлений должен быть доступен', () => {
        expect(notificationsResolver).toBeDefined();
    });

    describe('ACTION_COURSE_ACCESS_STUDENT_INVITE', () => {
        beforeEach(async () => {
            // во время запроса настроек считываем и сохраняем локаль пользователя
            await userSession2.getUserNotifications({ viewed: false }, { skip: 0, take: 100 }, LocaleEnum.en);
            await notificationConsumer.handleNotificationsMessageV2(
                createMessageV2(TestAction.ACTION_COURSE_ACCESS_STUDENT_INVITE, getUsers([1, 2])),
            );
        });

        it('Должен уведомить по email на русском языке', async () => {
            expect(emailSpy).toHaveBeenCalledTimes(2);

            expect(emailSpy).toHaveBeenCalledWith({
                schoolUuid: expect.any(String),
                template: 'notification',
                subject: 'Вам открыт доступ к новому курсу',
                fromName: 'Школа',
                fromEmail: '<EMAIL>',
                recipients: ['<EMAIL>'],
                body: {
                    primaryColor: '#17b198',
                    hideMobileAppLinks: true,
                    isWhiteLabel: true,
                    schoolLogoUrl: null,
                    textOnly: false,
                    title: 'Новый курс доступен для прохождения в вашем личном кабинете<br>',
                    html: 'Вам открыт доступ к курсу <b>"Курс молодого сурка"</b>.<br>Чтобы начать обучение, войдите в аккаунт:<br>',
                    buttonText: 'Войти в аккаунт',
                    redirectUrl: 'https://dev.sksp.site/course/78',
                    unSubscribeUrl: 'https://some.url.com/unsubscribe/1',
                },
            });
        });

        it('Должен уведомить по email на английском языке', async () => {
            expect(emailSpy).toHaveBeenCalledTimes(2);

            expect(emailSpy).toHaveBeenCalledWith({
                schoolUuid: expect.any(String),
                template: 'notification',
                subject: "You've been granted access to a new course",
                fromName: 'Школа',
                fromEmail: '<EMAIL>',
                recipients: ['<EMAIL>'],
                body: {
                    primaryColor: '#17b198',
                    hideMobileAppLinks: true,
                    isWhiteLabel: true,
                    schoolLogoUrl: null,
                    textOnly: false,
                    title: 'A new course is available for you to complete in your personal account<br>',
                    html: 'You have been invited to the course <b>"Курс молодого сурка"</b>.<br>To start learning, log in to your account:<br>',
                    buttonText: 'Log in to your account',
                    redirectUrl: 'https://dev.sksp.site/course/78',
                    unSubscribeUrl: 'https://some.url.com/unsubscribe/2',
                },
            });
        });

        it('Должен уведомить в телеграмм на русском языке', async () => {
            expect(telegramSpy).toHaveBeenCalledTimes(2);

            expect(telegramSpy).toHaveBeenCalledWith({
                text: 'Вам открыт доступ к курсу <b>"Курс молодого сурка"</b> https://dev.sksp.site/course/78',
                unionAuthKey: '6fc112f6-e471-4d83-94c2-ff447f43d5e0',
            });
        });

        it('Должен уведомить в телеграмм на английском языке', async () => {
            expect(telegramSpy).toHaveBeenCalledTimes(2);

            expect(telegramSpy).toHaveBeenCalledWith({
                text: 'You have been invited to the course <b>"Курс молодого сурка"</b> https://dev.sksp.site/course/78',
                unionAuthKey: '6fc112f6-e471-4d83-94c2-ff447f43d5e0',
            });
        });

        it('Должен уведомить на платформе на русском языке', async () => {
            expect(wsSpy).toHaveBeenCalledTimes(2);

            expect(wsSpy).toHaveBeenCalledWith({
                id: expect.any(String),
                userId: 1,
                schoolId: 1,
                userUuid: '6fc112f6-e471-4d83-94c2-ff447f43d5e1',
                schoolUuid: '6fc112f6-e471-4d83-94c2-ff447f43d5e3',
                type: null,
                action: 'ACTION_COURSE_ACCESS_STUDENT_INVITE',
                text: 'Вам открыт доступ к курсу <b>"Курс молодого сурка"</b>',
                meta: {
                    whiteLabel: {
                        primaryColor: '#17b198',
                        hideMobileAppLinks: true,
                        isWhiteLabel: true,
                        schoolLogoUrl: null,
                        textOnly: false,
                    },
                    course: { id: 78, name: 'Курс молодого сурка', uuid: '421c66b4-8f92-4c43-aab7-c4d571dff99b' },
                    domain: 'dev.sksp.site',
                    url: 'https://dev.sksp.site/course/78',
                },
                viewed: false,
                createdAt: expect.any(Number),
                updatedAt: expect.any(Number),
            });
        });

        it('Должен уведомить на платформе на английском языке', async () => {
            expect(wsSpy).toHaveBeenCalledTimes(2);

            expect(wsSpy).toHaveBeenCalledWith({
                id: expect.any(String),
                userId: 2,
                schoolId: 1,
                userUuid: '6fc112f6-e471-4d83-94c2-ff447f43d5e2',
                schoolUuid: '6fc112f6-e471-4d83-94c2-ff447f43d5e3',
                type: null,
                action: 'ACTION_COURSE_ACCESS_STUDENT_INVITE',
                text: 'You have been invited to the course <b>"Курс молодого сурка"</b>',
                meta: {
                    whiteLabel: {
                        primaryColor: '#17b198',
                        hideMobileAppLinks: true,
                        isWhiteLabel: true,
                        schoolLogoUrl: null,
                        textOnly: false,
                    },
                    course: { id: 78, name: 'Курс молодого сурка', uuid: '421c66b4-8f92-4c43-aab7-c4d571dff99b' },
                    domain: 'dev.sksp.site',
                    url: 'https://dev.sksp.site/course/78',
                },
                viewed: false,
                createdAt: expect.any(Number),
                updatedAt: expect.any(Number),
            });
        });

        it('Должен сохранить уведомление в базе данных', async () => {
            const result1 = await userSession1.getUserNotifications({ viewed: false }, { skip: 0, take: 10 });
            const result2 = await userSession2.getUserNotifications({ viewed: false }, { skip: 0, take: 10 });

            expect(result1.notifications.length).toEqual(1);
            expect(result2.notifications.length).toEqual(1);

            expect(result1.notifications[0].action).toBe('ACTION_COURSE_ACCESS_STUDENT_INVITE');
            expect(result2.notifications[0].action).toBe('ACTION_COURSE_ACCESS_STUDENT_INVITE');
        });
    });

    describe('ACTION_COURSE_ACCESS_STUDENT_INVITE_AND_REGISTER', () => {
        beforeEach(async () => {
            // во время запроса настроек считываем и сохраняем локаль пользователя
            await userSession2.getUserNotifications({ viewed: false }, { skip: 0, take: 100 }, LocaleEnum.en);
            await notificationConsumer.handleNotificationsMessageV2(
                createMessageV2(TestAction.ACTION_COURSE_ACCESS_STUDENT_INVITE_AND_REGISTER, getUsers([1, 2])),
            );
        });

        it('Должен уведомить по email на русском языке с регистрационным текстом', async () => {
            expect(emailSpy).toHaveBeenCalledTimes(2);

            expect(emailSpy).toHaveBeenCalledWith({
                schoolUuid: expect.any(String),
                template: 'notification',
                subject: 'Вам открыт доступ к новому курсу',
                fromName: 'Школа',
                fromEmail: '<EMAIL>',
                recipients: ['<EMAIL>'],
                body: {
                    primaryColor: '#17b198',
                    hideMobileAppLinks: true,
                    isWhiteLabel: true,
                    schoolLogoUrl: null,
                    textOnly: false,
                    title: 'Осталось установить пароль и войти в аккаунт<br>',
                    html: 'Вам открыт доступ к курсу <b>"Курс молодого сурка"</b>.<br>Чтобы войти в аккаунт и начать обучение, нажмите на кнопку ниже и установите пароль<br>',
                    buttonText: 'Войти в аккаунт',
                    redirectUrl: 'https://dev.sksp.site/course/78',
                    unSubscribeUrl: 'https://some.url.com/unsubscribe/1',
                },
            });
        });

        it('Должен уведомить по email на английском языке с регистрационным текстом', async () => {
            expect(emailSpy).toHaveBeenCalledTimes(2);

            expect(emailSpy).toHaveBeenCalledWith({
                schoolUuid: expect.any(String),
                template: 'notification',
                subject: "You've been granted access to a new course",
                fromName: 'Школа',
                fromEmail: '<EMAIL>',
                recipients: ['<EMAIL>'],
                body: {
                    primaryColor: '#17b198',
                    hideMobileAppLinks: true,
                    isWhiteLabel: true,
                    schoolLogoUrl: null,
                    textOnly: false,
                    title: 'Set a password and log in to your account<br>',
                    html: 'You have been invited to the course <b>"Курс молодого сурка"</b>.<br>To log in to your account and start learning, click the button below and set a password<br>',
                    buttonText: 'Log in to your account',
                    redirectUrl: 'https://dev.sksp.site/course/78',
                    unSubscribeUrl: 'https://some.url.com/unsubscribe/2',
                },
            });
        });

        it('Должен уведомить в телеграмм на русском языке', async () => {
            expect(telegramSpy).toHaveBeenCalledTimes(2);

            expect(telegramSpy).toHaveBeenCalledWith({
                text: 'Вам открыт доступ к курсу <b>"Курс молодого сурка"</b> https://dev.sksp.site/course/78',
                unionAuthKey: '6fc112f6-e471-4d83-94c2-ff447f43d5e0',
            });
        });

        it('Должен уведомить в телеграмм на английском языке', async () => {
            expect(telegramSpy).toHaveBeenCalledTimes(2);

            expect(telegramSpy).toHaveBeenCalledWith({
                text: 'You have been invited to the course <b>"Курс молодого сурка"</b> https://dev.sksp.site/course/78',
                unionAuthKey: '6fc112f6-e471-4d83-94c2-ff447f43d5e0',
            });
        });

        it('Должен уведомить на платформе на русском языке', async () => {
            expect(wsSpy).toHaveBeenCalledTimes(2);

            expect(wsSpy).toHaveBeenCalledWith({
                id: expect.any(String),
                userId: 1,
                schoolId: 1,
                userUuid: '6fc112f6-e471-4d83-94c2-ff447f43d5e1',
                schoolUuid: '6fc112f6-e471-4d83-94c2-ff447f43d5e3',
                type: null,
                action: 'ACTION_COURSE_ACCESS_STUDENT_INVITE_AND_REGISTER',
                text: 'Вам открыт доступ к курсу <b>"Курс молодого сурка"</b>',
                meta: {
                    whiteLabel: {
                        primaryColor: '#17b198',
                        hideMobileAppLinks: true,
                        isWhiteLabel: true,
                        schoolLogoUrl: null,
                        textOnly: false,
                    },
                    course: { id: 78, name: 'Курс молодого сурка', uuid: '421c66b4-8f92-4c43-aab7-c4d571dff99b' },
                    domain: 'dev.sksp.site',
                    url: 'https://dev.sksp.site/course/78',
                },
                viewed: false,
                createdAt: expect.any(Number),
                updatedAt: expect.any(Number),
            });
        });

        it('Должен уведомить на платформе на английском языке', async () => {
            expect(wsSpy).toHaveBeenCalledTimes(2);

            expect(wsSpy).toHaveBeenCalledWith({
                id: expect.any(String),
                userId: 2,
                schoolId: 1,
                userUuid: '6fc112f6-e471-4d83-94c2-ff447f43d5e2',
                schoolUuid: '6fc112f6-e471-4d83-94c2-ff447f43d5e3',
                type: null,
                action: 'ACTION_COURSE_ACCESS_STUDENT_INVITE_AND_REGISTER',
                text: 'You have been invited to the course <b>"Курс молодого сурка"</b>',
                meta: {
                    whiteLabel: {
                        primaryColor: '#17b198',
                        hideMobileAppLinks: true,
                        isWhiteLabel: true,
                        schoolLogoUrl: null,
                        textOnly: false,
                    },
                    course: { id: 78, name: 'Курс молодого сурка', uuid: '421c66b4-8f92-4c43-aab7-c4d571dff99b' },
                    domain: 'dev.sksp.site',
                    url: 'https://dev.sksp.site/course/78',
                },
                viewed: false,
                createdAt: expect.any(Number),
                updatedAt: expect.any(Number),
            });
        });

        it('Должен сохранить уведомление в базе данных', async () => {
            const result1 = await userSession1.getUserNotifications({ viewed: false }, { skip: 0, take: 10 });
            const result2 = await userSession2.getUserNotifications({ viewed: false }, { skip: 0, take: 10 });

            expect(result1.notifications.length).toEqual(1);
            expect(result2.notifications.length).toEqual(1);

            expect(result1.notifications[0].action).toBe('ACTION_COURSE_ACCESS_STUDENT_INVITE_AND_REGISTER');
            expect(result2.notifications[0].action).toBe('ACTION_COURSE_ACCESS_STUDENT_INVITE_AND_REGISTER');
        });

        it('Должен использовать правильную мета-схему для регистрации', async () => {
            const result1 = await userSession1.getUserNotifications({ viewed: false }, { skip: 0, take: 10 });

            expect(result1.notifications[0].meta).toEqual({
                whiteLabel: {
                    primaryColor: '#17b198',
                    hideMobileAppLinks: true,
                    isWhiteLabel: true,
                    schoolLogoUrl: null,
                    textOnly: false,
                },
                course: { id: 78, name: 'Курс молодого сурка', uuid: '421c66b4-8f92-4c43-aab7-c4d571dff99b' },
                domain: 'dev.sksp.site',
                url: 'https://dev.sksp.site/course/78',
            });
        });
    });
});
