{"name": "notifications", "version": "2.1.7", "description": "", "author": "Денис Благовещенский", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"tests/**/*.ts\"", "prelint": "prisma generate", "lint": "eslint \"{src,tests}/**/*.ts\"", "lint:fix": "eslint \"{src,tests}/**/*.ts\" --fix", "start": "node dist/src/main.js", "start:dev": "node dist/src/main.js --watch", "start:debug": "node dist/src/main.js --debug --watch", "start:prod": "node dist/src/main.js", "prisma:gen": "prisma generate", "db:push": "prisma db push", "seed:run": "ts-node prisma/seed.ts", "migrate:status": "migrate-mongo status", "migrate:up": "migrate-mongo up", "migrate:down": "migrate-mongo down", "test": "exit 0", "test1": "jest --config='tests/jest.config.js' --runInBand -b", "test:unit": "jest --config='tests/jest.unit.config.js'", "check-app1": "node dist/check-app.js", "check-app": "exit 0", "release": "semantic-release", "prepare": "husky"}, "dependencies": {"@apollo/server": "^4.11.3", "@apollo/subgraph": "2.2.3", "@nestjs/apollo": "^13.0.3", "@nestjs/common": "^11.0.10", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.10", "@nestjs/cqrs": "^11.0.2", "@nestjs/event-emitter": "^3.0.1", "@nestjs/graphql": "^13.0.3", "@nestjs/platform-express": "^11.0.10", "@nestjs/schedule": "^5.0.1", "@nestjs/terminus": "^11.0.0", "@prisma/client": "^6.4.1", "@skillspace/access": "1.0.5", "@skillspace/amqp-contracts": "^2.9.1", "@skillspace/common": "^1.2.0", "@skillspace/cqrs": "1.0.0", "@skillspace/graphql": "1.3.1", "@skillspace/grpc": "^1.3.1", "@skillspace/logger": "4.2.0", "@skillspace/tracing": "^1.0.1", "@skillspace/utils": "0.1.1", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.5", "bson": "^6.10.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.7", "graphql": "^16.10.0", "graphql-type-json": "^0.3.2", "prisma": "^6.4.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "zod": "^3.24.2"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/compat": "^1.2.7", "@golevelup/ts-jest": "^0.6.2", "@nestjs/cli": "^11.0.4", "@nestjs/schematics": "^11.0.1", "@nestjs/testing": "^11.0.10", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/gitlab": "^13.2.5", "@skillspace/eslint-service": "^1.3.0", "@skillspace/hermes": "^1.0.3", "@testcontainers/rabbitmq": "^10.18.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.13.5", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.24.1", "@typescript-eslint/parser": "^8.24.1", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.1", "eslint-import-resolver-typescript": "^4.4.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.0.0", "graphql-tag": "^2.12.6", "husky": "^9.1.7", "jest": "^29.7.0", "mongodb-memory-server": "^10.1.4", "prettier": "^3.5.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.6", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "4.2.0", "typescript": "^5.7.3"}, "pnpm": {"overrides": {}}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}